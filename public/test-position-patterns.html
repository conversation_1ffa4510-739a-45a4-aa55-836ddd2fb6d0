<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON> - Position <PERSON>terns</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background-color: #f5f5f5;
            display: flex;
            gap: 20px;
        }
        .controls { 
            width: 300px; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .preview { 
            flex: 1; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="color"] {
            height: 40px;
        }
        .pattern-preview {
            background: #fafafa;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
        }
        h3 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        .expected {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
        }
        button:hover {
            background: #2980b9;
        }
        #output-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>🎯 Configuração dos Position Patterns</h3>
        
        <div class="expected">
            <strong>🎯 Objetivo:</strong><br>
            Círculo preenchido sólido + anel externo
        </div>

        <div class="control-group">
            <label for="cornerType">Tipo do Canto (cornerSquareType):</label>
            <select id="cornerType">
                <option value="square">Square (Quadrado)</option>
                <option value="dot" selected>Dot (Círculo)</option>
                <option value="extra-rounded">Extra Rounded</option>
            </select>
        </div>

        <div class="control-group">
            <label for="cornerColor">Cor dos Cantos:</label>
            <input type="color" id="cornerColor" value="#e74c3c">
        </div>

        <div class="control-group">
            <label for="centerType">Tipo do Centro (cornerDotType):</label>
            <select id="centerType">
                <option value="square">Square (Quadrado)</option>
                <option value="dot" selected>Dot (Círculo)</option>
            </select>
        </div>

        <div class="control-group">
            <label for="centerColor">Cor do Centro:</label>
            <input type="color" id="centerColor" value="#3498db">
        </div>

        <div class="control-group">
            <label for="patternSize">Tamanho do Pattern:</label>
            <input type="range" id="patternSize" min="50" max="200" value="100">
            <span id="sizeValue">100px</span>
        </div>

        <button onclick="updatePattern()">🔄 Atualizar Pattern</button>
        <button onclick="testAllTypes()">🧪 Testar Todos os Tipos</button>
        
        <div id="output-info">
            Configuração atual:
            cornerSquareType: dot
            cornerDotType: dot
        </div>
    </div>

    <div class="preview">
        <h3>🔍 Preview dos Position Patterns</h3>
        
        <div class="pattern-preview">
            <h4>Position Pattern Individual (Teste)</h4>
            <div id="single-pattern"></div>
        </div>

        <div class="pattern-preview">
            <h4>QR Code Completo (para validação)</h4>
            <div id="full-qr"></div>
        </div>

        <div id="debug-info" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <h4>🔧 Debug - Elementos SVG Gerados:</h4>
            <div id="svg-structure"></div>
        </div>
    </div>

    <script src="qr-code-styling.js"></script>
    <script>
        let currentQR = null;

        function updatePattern() {
            const cornerType = document.getElementById('cornerType').value;
            const cornerColor = document.getElementById('cornerColor').value;
            const centerType = document.getElementById('centerType').value;
            const centerColor = document.getElementById('centerColor').value;
            const patternSize = document.getElementById('patternSize').value;
            
            // Update size display
            document.getElementById('sizeValue').textContent = patternSize + 'px';
            
            // Update info
            document.getElementById('output-info').textContent = `Configuração atual:
cornerSquareType: ${cornerType}
cornerDotType: ${centerType}
cornerColor: ${cornerColor}
centerColor: ${centerColor}
size: ${patternSize}px`;

            // Clear previous content
            document.getElementById('single-pattern').innerHTML = '';
            document.getElementById('full-qr').innerHTML = '';
            
            // Create a simple QR code to test position patterns
            currentQR = new QRCodeStyling({
                width: parseInt(patternSize) * 3,
                height: parseInt(patternSize) * 3,
                type: "svg",
                data: "TEST",
                margin: 10,
                dotsOptions: {
                    color: "#000000",
                    type: "square"
                },
                cornersSquareOptions: {
                    color: cornerColor,
                    type: cornerType
                },
                cornersDotOptions: {
                    color: centerColor,
                    type: centerType
                },
                backgroundOptions: {
                    color: "#ffffff"
                }
            });

            // Render full QR
            currentQR.append(document.getElementById('full-qr'));
            
            // Create individual pattern test
            createSinglePattern(cornerType, cornerColor, centerType, centerColor, parseInt(patternSize));
            
            // Debug SVG structure
            setTimeout(debugSvgStructure, 100);
        }

        function createSinglePattern(cornerType, cornerColor, centerType, centerColor, size) {
            // Create a minimal SVG just for the position pattern
            const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
            svg.setAttribute("width", size);
            svg.setAttribute("height", size);
            svg.setAttribute("viewBox", `0 0 ${size} ${size}`);
            svg.style.border = "1px solid #ddd";
            
            // Try to manually create the corner square element
            try {
                // Import the corner square class (this might need adjustment)
                // For now, let's create a simple representation
                createPatternVisualization(svg, cornerType, cornerColor, centerType, centerColor, size);
            } catch (error) {
                console.error('Error creating pattern:', error);
                svg.innerHTML = `<text x="50%" y="50%" text-anchor="middle" fill="red">Error: ${error.message}</text>`;
            }
            
            document.getElementById('single-pattern').appendChild(svg);
        }

        function createPatternVisualization(svg, cornerType, cornerColor, centerType, centerColor, size) {
            const centerX = size / 2;
            const centerY = size / 2;
            const outerRadius = size * 0.4;
            const innerRadius = size * 0.1;
            
            if (cornerType === 'dot') {
                // Create ring + center circle visualization
                const ring = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                ring.setAttribute("cx", centerX);
                ring.setAttribute("cy", centerY);
                ring.setAttribute("r", outerRadius * 0.75);
                ring.setAttribute("fill", "none");
                ring.setAttribute("stroke", cornerColor);
                ring.setAttribute("stroke-width", outerRadius * 0.35);
                svg.appendChild(ring);
                
                const center = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                center.setAttribute("cx", centerX);
                center.setAttribute("cy", centerY);
                center.setAttribute("r", innerRadius);
                center.setAttribute("fill", centerColor);
                svg.appendChild(center);
                
            } else if (cornerType === 'square') {
                // Create square pattern
                const square = document.createElementNS("http://www.w3.org/2000/svg", "rect");
                square.setAttribute("x", centerX - outerRadius);
                square.setAttribute("y", centerY - outerRadius);
                square.setAttribute("width", outerRadius * 2);
                square.setAttribute("height", outerRadius * 2);
                square.setAttribute("fill", cornerColor);
                svg.appendChild(square);
                
                const centerSquare = document.createElementNS("http://www.w3.org/2000/svg", "rect");
                centerSquare.setAttribute("x", centerX - innerRadius);
                centerSquare.setAttribute("y", centerY - innerRadius);
                centerSquare.setAttribute("width", innerRadius * 2);
                centerSquare.setAttribute("height", innerRadius * 2);
                centerSquare.setAttribute("fill", centerColor);
                svg.appendChild(centerSquare);
            }
        }

        function debugSvgStructure() {
            if (!currentQR) return;
            
            const qrElement = document.querySelector('#full-qr svg');
            if (qrElement) {
                // Find position pattern elements
                const groups = qrElement.querySelectorAll('g');
                let patternInfo = '';
                
                groups.forEach((group, index) => {
                    const children = group.children;
                    if (children.length > 0) {
                        patternInfo += `Grupo ${index}: ${children.length} elementos\n`;
                        for (let i = 0; i < children.length; i++) {
                            const child = children[i];
                            patternInfo += `  - ${child.tagName}`;
                            if (child.tagName === 'circle') {
                                patternInfo += ` (r=${child.getAttribute('r')}, fill=${child.getAttribute('fill')}, stroke=${child.getAttribute('stroke')})`;
                            }
                            patternInfo += '\n';
                        }
                    }
                });
                
                document.getElementById('svg-structure').textContent = patternInfo || 'Nenhuma estrutura de grupo encontrada';
            }
        }

        function testAllTypes() {
            const types = ['square', 'dot', 'extra-rounded'];
            let currentIndex = 0;
            
            function nextTest() {
                if (currentIndex < types.length) {
                    document.getElementById('cornerType').value = types[currentIndex];
                    updatePattern();
                    currentIndex++;
                    setTimeout(nextTest, 2000); // Wait 2 seconds between tests
                }
            }
            
            nextTest();
        }

        // Initialize
        window.onload = function() {
            updatePattern();
        };

        console.log('🎯 TESTE DINÂMICO DE POSITION PATTERNS');
        console.log('=====================================');
        console.log('Use os controles para testar diferentes configurações');
        console.log('Objetivo: Encontrar a configuração que gera círculo sólido + anel externo');
    </script>
</body>
</html>